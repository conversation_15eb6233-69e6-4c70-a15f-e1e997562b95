{"version": 3, "sources": ["../../../../src/server/web/spec-extension/user-agent.ts"], "sourcesContent": ["import parseua from 'next/dist/compiled/ua-parser-js'\n\ninterface UserAgent {\n  isBot: boolean\n  ua: string\n  browser: {\n    name?: string\n    version?: string\n    major?: string\n  }\n  device: {\n    model?: string\n    type?: string\n    vendor?: string\n  }\n  engine: {\n    name?: string\n    version?: string\n  }\n  os: {\n    name?: string\n    version?: string\n  }\n  cpu: {\n    architecture?: string\n  }\n}\n\nexport function isBot(input: string): boolean {\n  return /Googlebot|Mediapartners-Google|AdsBot-Google|googleweblight|Storebot-Google|Google-PageRenderer|Google-InspectionTool|Bingbot|BingPreview|Slurp|DuckDuckBot|baiduspider|yandex|sogou|LinkedInBot|bitlybot|tumblr|vkShare|quora link preview|facebookexternalhit|facebookcatalog|Twitterbot|applebot|redditbot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|ia_archiver/i.test(\n    input\n  )\n}\n\nexport function userAgentFromString(input: string | undefined): UserAgent {\n  return {\n    ...parseua(input),\n    isBot: input === undefined ? false : isBot(input),\n  }\n}\n\nexport function userAgent({ headers }: { headers: Headers }): UserAgent {\n  return userAgentFromString(headers.get('user-agent') || undefined)\n}\n"], "names": ["isBot", "userAgent", "userAgentFromString", "input", "test", "parseua", "undefined", "headers", "get"], "mappings": ";;;;;;;;;;;;;;;;IA4BgBA,KAAK;eAALA;;IAaAC,SAAS;eAATA;;IAPAC,mBAAmB;eAAnBA;;;mEAlCI;;;;;;AA4Bb,SAASF,MAAMG,KAAa;IACjC,OAAO,0WAA0WC,IAAI,CACnXD;AAEJ;AAEO,SAASD,oBAAoBC,KAAyB;IAC3D,OAAO;QACL,GAAGE,IAAAA,mBAAO,EAACF,MAAM;QACjBH,OAAOG,UAAUG,YAAY,QAAQN,MAAMG;IAC7C;AACF;AAEO,SAASF,UAAU,EAAEM,OAAO,EAAwB;IACzD,OAAOL,oBAAoBK,QAAQC,GAAG,CAAC,iBAAiBF;AAC1D", "ignoreList": [0]}