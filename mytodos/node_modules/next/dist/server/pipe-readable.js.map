{"version": 3, "sources": ["../../src/server/pipe-readable.ts"], "sourcesContent": ["import type { ServerResponse } from 'node:http'\n\nimport {\n  ResponseAbortedName,\n  createAbortController,\n} from './web/spec-extension/adapters/next-request'\nimport { DetachedPromise } from '../lib/detached-promise'\nimport { getTracer } from './lib/trace/tracer'\nimport { NextNodeServerSpan } from './lib/trace/constants'\nimport { getClientComponentLoaderMetrics } from './client-component-renderer-logger'\n\nexport function isAbortError(e: any): e is Error & { name: 'AbortError' } {\n  return e?.name === 'AbortError' || e?.name === ResponseAbortedName\n}\n\nfunction createWriterFromResponse(\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n): WritableStream<Uint8Array> {\n  let started = false\n\n  // Create a promise that will resolve once the response has drained. See\n  // https://nodejs.org/api/stream.html#stream_event_drain\n  let drained = new DetachedPromise<void>()\n  function onDrain() {\n    drained.resolve()\n  }\n  res.on('drain', onDrain)\n\n  // If the finish event fires, it means we shouldn't block and wait for the\n  // drain event.\n  res.once('close', () => {\n    res.off('drain', onDrain)\n    drained.resolve()\n  })\n\n  // Create a promise that will resolve once the response has finished. See\n  // https://nodejs.org/api/http.html#event-finish_1\n  const finished = new DetachedPromise<void>()\n  res.once('finish', () => {\n    finished.resolve()\n  })\n\n  // Create a writable stream that will write to the response.\n  return new WritableStream<Uint8Array>({\n    write: async (chunk) => {\n      // You'd think we'd want to use `start` instead of placing this in `write`\n      // but this ensures that we don't actually flush the headers until we've\n      // started writing chunks.\n      if (!started) {\n        started = true\n\n        if (\n          'performance' in globalThis &&\n          process.env.NEXT_OTEL_PERFORMANCE_PREFIX\n        ) {\n          const metrics = getClientComponentLoaderMetrics()\n          if (metrics) {\n            performance.measure(\n              `${process.env.NEXT_OTEL_PERFORMANCE_PREFIX}:next-client-component-loading`,\n              {\n                start: metrics.clientComponentLoadStart,\n                end:\n                  metrics.clientComponentLoadStart +\n                  metrics.clientComponentLoadTimes,\n              }\n            )\n          }\n        }\n\n        res.flushHeaders()\n        getTracer().trace(\n          NextNodeServerSpan.startResponse,\n          {\n            spanName: 'start response',\n          },\n          () => undefined\n        )\n      }\n\n      try {\n        const ok = res.write(chunk)\n\n        // Added by the `compression` middleware, this is a function that will\n        // flush the partially-compressed response to the client.\n        if ('flush' in res && typeof res.flush === 'function') {\n          res.flush()\n        }\n\n        // If the write returns false, it means there's some backpressure, so\n        // wait until it's streamed before continuing.\n        if (!ok) {\n          await drained.promise\n\n          // Reset the drained promise so that we can wait for the next drain event.\n          drained = new DetachedPromise<void>()\n        }\n      } catch (err) {\n        res.end()\n        throw new Error('failed to write chunk to response', { cause: err })\n      }\n    },\n    abort: (err) => {\n      if (res.writableFinished) return\n\n      res.destroy(err)\n    },\n    close: async () => {\n      // if a waitUntil promise was passed, wait for it to resolve before\n      // ending the response.\n      if (waitUntilForEnd) {\n        await waitUntilForEnd\n      }\n\n      if (res.writableFinished) return\n\n      res.end()\n      return finished.promise\n    },\n  })\n}\n\nexport async function pipeToNodeResponse(\n  readable: ReadableStream<Uint8Array>,\n  res: ServerResponse,\n  waitUntilForEnd?: Promise<unknown>\n) {\n  try {\n    // If the response has already errored, then just return now.\n    const { errored, destroyed } = res\n    if (errored || destroyed) return\n\n    // Create a new AbortController so that we can abort the readable if the\n    // client disconnects.\n    const controller = createAbortController(res)\n\n    const writer = createWriterFromResponse(res, waitUntilForEnd)\n\n    await readable.pipeTo(writer, { signal: controller.signal })\n  } catch (err: any) {\n    // If this isn't related to an abort error, re-throw it.\n    if (isAbortError(err)) return\n\n    throw new Error('failed to pipe response', { cause: err })\n  }\n}\n"], "names": ["isAbortError", "pipeToNodeResponse", "e", "name", "ResponseAbortedName", "createWriterFromResponse", "res", "waitUntilForEnd", "started", "drained", "Detached<PERSON>romise", "onDrain", "resolve", "on", "once", "off", "finished", "WritableStream", "write", "chunk", "globalThis", "process", "env", "NEXT_OTEL_PERFORMANCE_PREFIX", "metrics", "getClientComponentLoaderMetrics", "performance", "measure", "start", "clientComponentLoadStart", "end", "clientComponentLoadTimes", "flushHeaders", "getTracer", "trace", "NextNodeServerSpan", "startResponse", "spanName", "undefined", "ok", "flush", "promise", "err", "Error", "cause", "abort", "writableFinished", "destroy", "close", "readable", "errored", "destroyed", "controller", "createAbortController", "writer", "pipeTo", "signal"], "mappings": ";;;;;;;;;;;;;;;IAWgBA,YAAY;eAAZA;;IA+GMC,kBAAkB;eAAlBA;;;6BArHf;iCACyB;wBACN;2BACS;+CACa;AAEzC,SAASD,aAAaE,CAAM;IACjC,OAAOA,CAAAA,qBAAAA,EAAGC,IAAI,MAAK,gBAAgBD,CAAAA,qBAAAA,EAAGC,IAAI,MAAKC,gCAAmB;AACpE;AAEA,SAASC,yBACPC,GAAmB,EACnBC,eAAkC;IAElC,IAAIC,UAAU;IAEd,wEAAwE;IACxE,wDAAwD;IACxD,IAAIC,UAAU,IAAIC,gCAAe;IACjC,SAASC;QACPF,QAAQG,OAAO;IACjB;IACAN,IAAIO,EAAE,CAAC,SAASF;IAEhB,0EAA0E;IAC1E,eAAe;IACfL,IAAIQ,IAAI,CAAC,SAAS;QAChBR,IAAIS,GAAG,CAAC,SAASJ;QACjBF,QAAQG,OAAO;IACjB;IAEA,yEAAyE;IACzE,kDAAkD;IAClD,MAAMI,WAAW,IAAIN,gCAAe;IACpCJ,IAAIQ,IAAI,CAAC,UAAU;QACjBE,SAASJ,OAAO;IAClB;IAEA,4DAA4D;IAC5D,OAAO,IAAIK,eAA2B;QACpCC,OAAO,OAAOC;YACZ,0EAA0E;YAC1E,wEAAwE;YACxE,0BAA0B;YAC1B,IAAI,CAACX,SAAS;gBACZA,UAAU;gBAEV,IACE,iBAAiBY,cACjBC,QAAQC,GAAG,CAACC,4BAA4B,EACxC;oBACA,MAAMC,UAAUC,IAAAA,8DAA+B;oBAC/C,IAAID,SAAS;wBACXE,YAAYC,OAAO,CACjB,GAAGN,QAAQC,GAAG,CAACC,4BAA4B,CAAC,8BAA8B,CAAC,EAC3E;4BACEK,OAAOJ,QAAQK,wBAAwB;4BACvCC,KACEN,QAAQK,wBAAwB,GAChCL,QAAQO,wBAAwB;wBACpC;oBAEJ;gBACF;gBAEAzB,IAAI0B,YAAY;gBAChBC,IAAAA,iBAAS,IAAGC,KAAK,CACfC,6BAAkB,CAACC,aAAa,EAChC;oBACEC,UAAU;gBACZ,GACA,IAAMC;YAEV;YAEA,IAAI;gBACF,MAAMC,KAAKjC,IAAIY,KAAK,CAACC;gBAErB,sEAAsE;gBACtE,yDAAyD;gBACzD,IAAI,WAAWb,OAAO,OAAOA,IAAIkC,KAAK,KAAK,YAAY;oBACrDlC,IAAIkC,KAAK;gBACX;gBAEA,qEAAqE;gBACrE,8CAA8C;gBAC9C,IAAI,CAACD,IAAI;oBACP,MAAM9B,QAAQgC,OAAO;oBAErB,0EAA0E;oBAC1EhC,UAAU,IAAIC,gCAAe;gBAC/B;YACF,EAAE,OAAOgC,KAAK;gBACZpC,IAAIwB,GAAG;gBACP,MAAM,qBAA8D,CAA9D,IAAIa,MAAM,qCAAqC;oBAAEC,OAAOF;gBAAI,IAA5D,qBAAA;2BAAA;gCAAA;kCAAA;gBAA6D;YACrE;QACF;QACAG,OAAO,CAACH;YACN,IAAIpC,IAAIwC,gBAAgB,EAAE;YAE1BxC,IAAIyC,OAAO,CAACL;QACd;QACAM,OAAO;YACL,mEAAmE;YACnE,uBAAuB;YACvB,IAAIzC,iBAAiB;gBACnB,MAAMA;YACR;YAEA,IAAID,IAAIwC,gBAAgB,EAAE;YAE1BxC,IAAIwB,GAAG;YACP,OAAOd,SAASyB,OAAO;QACzB;IACF;AACF;AAEO,eAAexC,mBACpBgD,QAAoC,EACpC3C,GAAmB,EACnBC,eAAkC;IAElC,IAAI;QACF,6DAA6D;QAC7D,MAAM,EAAE2C,OAAO,EAAEC,SAAS,EAAE,GAAG7C;QAC/B,IAAI4C,WAAWC,WAAW;QAE1B,wEAAwE;QACxE,sBAAsB;QACtB,MAAMC,aAAaC,IAAAA,kCAAqB,EAAC/C;QAEzC,MAAMgD,SAASjD,yBAAyBC,KAAKC;QAE7C,MAAM0C,SAASM,MAAM,CAACD,QAAQ;YAAEE,QAAQJ,WAAWI,MAAM;QAAC;IAC5D,EAAE,OAAOd,KAAU;QACjB,wDAAwD;QACxD,IAAI1C,aAAa0C,MAAM;QAEvB,MAAM,qBAAoD,CAApD,IAAIC,MAAM,2BAA2B;YAAEC,OAAOF;QAAI,IAAlD,qBAAA;mBAAA;wBAAA;0BAAA;QAAmD;IAC3D;AACF", "ignoreList": [0]}