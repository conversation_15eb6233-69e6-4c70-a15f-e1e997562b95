"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[47],{2047:(e,t,a)=>{a.r(t),a.d(t,{TodoList:()=>T});var r=a(5155),s=a(2115),i=a(4616),n=a(9708),d=a(2085),o=a(2596),l=a(9688);function c(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,l.QP)((0,o.$)(t))}let u=(0,d.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function x(e){let{className:t,variant:a,size:s,asChild:i=!1,...d}=e,o=i?n.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:c(u({variant:a,size:s,className:t})),...d})}function m(e){let{className:t,type:a,...s}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:c("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...s})}function g(e){let{onAdd:t}=e,[a,n]=(0,s.useState)(""),d=e=>{e.preventDefault(),a.trim()&&(t(a.trim()),n(""))};return(0,r.jsxs)("form",{onSubmit:d,className:"flex gap-3",children:[(0,r.jsx)(m,{type:"text",placeholder:"添加新任务...",value:a,onChange:e=>n(e.target.value),onKeyPress:e=>{"Enter"===e.key&&d(e)},className:"flex-1 h-12 px-4 text-base font-medium placeholder:text-muted-foreground/60 border-2 border-border focus:border-primary transition-colors duration-200"}),(0,r.jsxs)(x,{type:"submit",disabled:!a.trim(),className:"h-12 px-6 font-semibold transition-all duration-200 hover:scale-[1.02] active:scale-[0.98] disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",children:[(0,r.jsx)(i.A,{className:"h-5 w-5 mr-2"}),"添加"]})]})}var f=a(2525),h=a(7484),p=a(5196);function b(e){let{className:t,...a}=e;return(0,r.jsx)(h.bL,{"data-slot":"checkbox",className:c("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(h.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(p.A,{className:"size-3.5"})})})}function v(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:c("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}var j=a(1078);function N(e){let{...t}=e;return(0,r.jsx)(j.bL,{"data-slot":"alert-dialog",...t})}function y(e){let{...t}=e;return(0,r.jsx)(j.l9,{"data-slot":"alert-dialog-trigger",...t})}function w(e){let{...t}=e;return(0,r.jsx)(j.ZL,{"data-slot":"alert-dialog-portal",...t})}function k(e){let{className:t,...a}=e;return(0,r.jsx)(j.hJ,{"data-slot":"alert-dialog-overlay",className:c("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function z(e){let{className:t,...a}=e;return(0,r.jsxs)(w,{children:[(0,r.jsx)(k,{}),(0,r.jsx)(j.UC,{"data-slot":"alert-dialog-content",className:c("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",t),...a})]})}function S(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-header",className:c("flex flex-col gap-2 text-center sm:text-left",t),...a})}function C(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"alert-dialog-footer",className:c("flex flex-col-reverse gap-2 sm:flex-row sm:justify-end",t),...a})}function E(e){let{className:t,...a}=e;return(0,r.jsx)(j.hE,{"data-slot":"alert-dialog-title",className:c("text-lg font-semibold",t),...a})}function _(e){let{className:t,...a}=e;return(0,r.jsx)(j.VY,{"data-slot":"alert-dialog-description",className:c("text-muted-foreground text-sm",t),...a})}function A(e){let{className:t,...a}=e;return(0,r.jsx)(j.rc,{className:c(u(),t),...a})}function D(e){let{className:t,...a}=e;return(0,r.jsx)(j.ZD,{className:c(u({variant:"outline"}),t),...a})}function L(e){let{todo:t,onToggle:a,onDelete:s}=e;return(0,r.jsx)(v,{className:"p-4 transition-all duration-200 hover:shadow-md hover:scale-[1.01] border-2 border-border hover:border-primary/20",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsx)(b,{id:t.id,checked:"completed"===t.status,onCheckedChange:()=>a(t.id),className:"w-5 h-5 transition-all duration-200"}),(0,r.jsx)("label",{htmlFor:t.id,className:"flex-1 cursor-pointer text-base font-medium transition-all duration-300 ".concat("completed"===t.status?"line-through text-muted-foreground opacity-60":"text-foreground hover:text-primary"),children:t.title}),"completed"===t.status&&(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-2 h-2 bg-primary rounded-full animate-pulse"}),(0,r.jsx)("span",{className:"text-xs font-medium text-primary",children:"已完成"})]}),(0,r.jsxs)(N,{children:[(0,r.jsx)(y,{asChild:!0,children:(0,r.jsx)(x,{variant:"ghost",size:"icon",className:"h-9 w-9 transition-all duration-200 hover:bg-destructive/10 hover:text-destructive hover:scale-110",children:(0,r.jsx)(f.A,{className:"h-4 w-4"})})}),(0,r.jsxs)(z,{className:"max-w-md",children:[(0,r.jsxs)(S,{className:"space-y-4",children:[(0,r.jsx)(E,{className:"text-xl font-semibold",children:"确认删除"}),(0,r.jsx)(_,{className:"text-base text-muted-foreground leading-relaxed",children:"您确定要删除这个任务吗？此操作无法撤销。"})]}),(0,r.jsxs)(C,{className:"gap-3",children:[(0,r.jsx)(D,{className:"font-medium",children:"取消"}),(0,r.jsx)(A,{onClick:()=>s(t.id),className:"bg-destructive hover:bg-destructive/90 font-medium",children:"删除"})]})]})]})]})})}function T(){let[e,t,a]=function(e,t){let[a,r]=(0,s.useState)(t),[i,n]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{try{{let t=window.localStorage.getItem(e);if(t){let e=JSON.parse(t);r(e)}}}catch(t){console.error('Error reading localStorage key "'.concat(e,'":'),t)}finally{n(!0)}},[e]),[a,t=>{try{let s=t instanceof Function?t(a):t;r(s),window.localStorage.setItem(e,JSON.stringify(s))}catch(t){console.error('Error setting localStorage key "'.concat(e,'":'),t)}},i]}("mytodos",[]),i=e=>{t(t=>t.map(t=>t.id===e?{...t,status:"pending"===t.status?"completed":"pending"}:t))},n=e=>{t(t=>t.filter(t=>t.id!==e))};return a?(0,r.jsxs)("div",{className:"w-full max-w-2xl mx-auto space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"text-center space-y-4 animate-slide-up",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-foreground",children:"MyTodos"}),(0,r.jsx)("p",{className:"text-lg font-medium text-muted-foreground leading-relaxed",children:"管理您的待办事项，提高工作效率"})]}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)(g,{onAdd:e=>{let a={id:Date.now().toString(),title:e,status:"pending",createdAt:Date.now()};t(e=>[...e,a])}})}),(0,r.jsx)("div",{className:"space-y-4",children:0===e.length?(0,r.jsx)("div",{className:"text-center py-16 animate-fade-in",children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)("div",{className:"w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center animate-bounce-subtle",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-muted-foreground",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-foreground",children:"还没有任务"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground max-w-sm mx-auto leading-relaxed",children:"创建第一个待办，开始提升效率吧！"})]})}):(0,r.jsx)("div",{className:"space-y-3",children:e.map(e=>(0,r.jsx)(L,{todo:e,onToggle:i,onDelete:n},e.id))})}),e.length>0&&(0,r.jsx)("div",{className:"text-center",children:(0,r.jsxs)("p",{className:"text-base font-medium text-muted-foreground",children:["总共 ",(0,r.jsx)("span",{className:"font-semibold text-foreground",children:e.length})," 个任务， 已完成 ",(0,r.jsx)("span",{className:"font-semibold text-primary",children:e.filter(e=>"completed"===e.status).length})," 个"]})})]}):(0,r.jsxs)("div",{className:"w-full max-w-2xl mx-auto space-y-8 animate-fade-in",children:[(0,r.jsxs)("div",{className:"text-center space-y-4 animate-slide-up",children:[(0,r.jsx)("h1",{className:"text-4xl font-bold tracking-tight text-foreground",children:"MyTodos"}),(0,r.jsx)("p",{className:"text-lg font-medium text-muted-foreground leading-relaxed",children:"管理您的待办事项，提高工作效率"})]}),(0,r.jsxs)("div",{className:"text-center py-16",children:[(0,r.jsx)("div",{className:"w-8 h-8 mx-auto border-4 border-primary border-t-transparent rounded-full animate-spin"}),(0,r.jsx)("p",{className:"mt-4 text-sm text-muted-foreground",children:"加载中..."})]})]})}}}]);