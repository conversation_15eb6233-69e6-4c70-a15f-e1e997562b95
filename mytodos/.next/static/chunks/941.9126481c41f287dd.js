"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[941],{1078:(e,t,r)=>{r.d(t,{rc:()=>tj,ZD:()=>tA,UC:()=>tR,VY:()=>tD,hJ:()=>tC,ZL:()=>tN,bL:()=>tk,hE:()=>tS,l9:()=>tE});var n,o,a,l=r(2115),i=r.t(l,2),s=r(6081),c=r(6101),u=r(5185),d=r(2712),f=i[" useId ".trim().toString()]||(()=>void 0),p=0;function m(e){let[t,r]=l.useState(f());return(0,d.N)(()=>{e||r(e=>e??String(p++))},[e]),e||(t?`radix-${t}`:"")}var v=r(5845),g=r(3655);function h(e){let t=l.useRef(e);return l.useEffect(()=>{t.current=e}),l.useMemo(()=>(...e)=>t.current?.(...e),[])}var b=r(5155),y="dismissableLayer.update",w=l.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),x=l.forwardRef((e,t)=>{var r,n;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:i,onPointerDownOutside:s,onFocusOutside:d,onInteractOutside:f,onDismiss:p,...m}=e,v=l.useContext(w),[x,N]=l.useState(null),C=null!=(n=null==x?void 0:x.ownerDocument)?n:null==(r=globalThis)?void 0:r.document,[,R]=l.useState({}),j=(0,c.s)(t,e=>N(e)),A=Array.from(v.layers),[S]=[...v.layersWithOutsidePointerEventsDisabled].slice(-1),D=A.indexOf(S),O=x?A.indexOf(x):-1,M=v.layersWithOutsidePointerEventsDisabled.size>0,P=O>=D,T=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=h(e),o=l.useRef(!1),a=l.useRef(()=>{});return l.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){E("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,r=[...v.branches].some(e=>e.contains(t));P&&!r&&(null==s||s(e),null==f||f(e),e.defaultPrevented||null==p||p())},C),L=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=h(e),o=l.useRef(!1);return l.useEffect(()=>{let e=e=>{e.target&&!o.current&&E("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...v.branches].some(e=>e.contains(t))&&(null==d||d(e),null==f||f(e),e.defaultPrevented||null==p||p())},C);return!function(e,t=globalThis?.document){let r=h(e);l.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{O===v.layers.size-1&&(null==i||i(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},C),l.useEffect(()=>{if(x)return a&&(0===v.layersWithOutsidePointerEventsDisabled.size&&(o=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),v.layersWithOutsidePointerEventsDisabled.add(x)),v.layers.add(x),k(),()=>{a&&1===v.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=o)}},[x,C,a,v]),l.useEffect(()=>()=>{x&&(v.layers.delete(x),v.layersWithOutsidePointerEventsDisabled.delete(x),k())},[x,v]),l.useEffect(()=>{let e=()=>R({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,b.jsx)(g.sG.div,{...m,ref:j,style:{pointerEvents:M?P?"auto":"none":void 0,...e.style},onFocusCapture:(0,u.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,u.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,u.m)(e.onPointerDownCapture,T.onPointerDownCapture)})});function k(){let e=new CustomEvent(y);document.dispatchEvent(e)}function E(e,t,r,n){let{discrete:o}=n,a=r.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&a.addEventListener(e,t,{once:!0}),o?(0,g.hO)(a,l):a.dispatchEvent(l)}x.displayName="DismissableLayer",l.forwardRef((e,t)=>{let r=l.useContext(w),n=l.useRef(null),o=(0,c.s)(t,n);return l.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,b.jsx)(g.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var N="focusScope.autoFocusOnMount",C="focusScope.autoFocusOnUnmount",R={bubbles:!1,cancelable:!0},j=l.forwardRef((e,t)=>{let{loop:r=!1,trapped:n=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...i}=e,[s,u]=l.useState(null),d=h(o),f=h(a),p=l.useRef(null),m=(0,c.s)(t,e=>u(e)),v=l.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;l.useEffect(()=>{if(n){let e=function(e){if(v.paused||!s)return;let t=e.target;s.contains(t)?p.current=t:D(p.current,{select:!0})},t=function(e){if(v.paused||!s)return;let t=e.relatedTarget;null!==t&&(s.contains(t)||D(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let r=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(s)});return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}},[n,s,v.paused]),l.useEffect(()=>{if(s){O.add(v);let e=document.activeElement;if(!s.contains(e)){let t=new CustomEvent(N,R);s.addEventListener(N,d),s.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=document.activeElement;for(let n of e)if(D(n,{select:t}),document.activeElement!==r)return}(A(s).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(s))}return()=>{s.removeEventListener(N,d),setTimeout(()=>{let t=new CustomEvent(C,R);s.addEventListener(C,f),s.dispatchEvent(t),t.defaultPrevented||D(null!=e?e:document.body,{select:!0}),s.removeEventListener(C,f),O.remove(v)},0)}}},[s,d,f,v]);let y=l.useCallback(e=>{if(!r&&!n||v.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[n,a]=function(e){let t=A(e);return[S(t,e),S(t.reverse(),e)]}(t);n&&a?e.shiftKey||o!==a?e.shiftKey&&o===n&&(e.preventDefault(),r&&D(a,{select:!0})):(e.preventDefault(),r&&D(n,{select:!0})):o===t&&e.preventDefault()}},[r,n,v.paused]);return(0,b.jsx)(g.sG.div,{tabIndex:-1,...i,ref:m,onKeyDown:y})});function A(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}function S(e,t){for(let r of e)if(!function(e,t){let{upTo:r}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===r||e!==r);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(r,{upTo:t}))return r}function D(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var r;let n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&(r=e)instanceof HTMLInputElement&&"select"in r&&t&&e.select()}}j.displayName="FocusScope";var O=function(){let e=[];return{add(t){let r=e[0];t!==r&&(null==r||r.pause()),(e=M(e,t)).unshift(t)},remove(t){var r;null==(r=(e=M(e,t))[0])||r.resume()}}}();function M(e,t){let r=[...e],n=r.indexOf(t);return -1!==n&&r.splice(n,1),r}var P=r(7650),T=l.forwardRef((e,t)=>{var r,n;let{container:o,...a}=e,[i,s]=l.useState(!1);(0,d.N)(()=>s(!0),[]);let c=o||i&&(null==(n=globalThis)||null==(r=n.document)?void 0:r.body);return c?P.createPortal((0,b.jsx)(g.sG.div,{...a,ref:t}),c):null});T.displayName="Portal";var L=r(8905),I=0;function z(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var _=function(){return(_=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function F(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}Object.create;Object.create;var W=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),$="width-before-scroll-bar";function G(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var B="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,U=new WeakMap;function K(e){return e}var q=function(e){void 0===e&&(e={});var t,r,n,o=(void 0===t&&(t=K),r=[],n=!1,{read:function(){if(n)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var o=t(e,n);return r.push(o),function(){r=r.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(n=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){n=!0;var t=[];if(r.length){var o=r;r=[],o.forEach(e),t=r}var a=function(){var r=t;t=[],r.forEach(e)},l=function(){return Promise.resolve().then(a)};l(),r={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),r}}}});return o.options=_({async:!0,ssr:!1},e),o}(),V=function(){},X=l.forwardRef(function(e,t){var r,n,o,a,i=l.useRef(null),s=l.useState({onScrollCapture:V,onWheelCapture:V,onTouchMoveCapture:V}),c=s[0],u=s[1],d=e.forwardProps,f=e.children,p=e.className,m=e.removeScrollBar,v=e.enabled,g=e.shards,h=e.sideCar,b=e.noRelative,y=e.noIsolation,w=e.inert,x=e.allowPinchZoom,k=e.as,E=e.gapMode,N=F(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=(r=[i,t],n=function(e){return r.forEach(function(t){return G(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:n,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=n,a=o.facade,B(function(){var e=U.get(a);if(e){var t=new Set(e),n=new Set(r),o=a.current;t.forEach(function(e){n.has(e)||G(e,null)}),n.forEach(function(e){t.has(e)||G(e,o)})}U.set(a,r)},[r]),a),R=_(_({},N),c);return l.createElement(l.Fragment,null,v&&l.createElement(h,{sideCar:q,removeScrollBar:m,shards:g,noRelative:b,noIsolation:y,inert:w,setCallbacks:u,allowPinchZoom:!!x,lockRef:i,gapMode:E}),d?l.cloneElement(l.Children.only(f),_(_({},R),{ref:C})):l.createElement(void 0===k?"div":k,_({},R,{className:p,ref:C}),f))});X.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},X.classNames={fullWidth:$,zeroRight:W};var Y=function(e){var t=e.sideCar,r=F(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var n=t.read();if(!n)throw Error("Sidecar medium not found");return l.createElement(n,_({},r))};Y.isSideCarExport=!0;var Z=function(){var e=0,t=null;return{add:function(n){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||r.nc;return t&&e.setAttribute("nonce",t),e}())){var o,l;(o=t).styleSheet?o.styleSheet.cssText=n:o.appendChild(document.createTextNode(n)),l=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(l)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},H=function(){var e=Z();return function(t,r){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&r])}},J=function(){var e=H();return function(t){return e(t.styles,t.dynamic),null}},Q={left:0,top:0,right:0,gap:0},ee=function(e){return parseInt(e||"",10)||0},et=function(e){var t=window.getComputedStyle(document.body),r=t["padding"===e?"paddingLeft":"marginLeft"],n=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ee(r),ee(n),ee(o)]},er=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Q;var t=et(e),r=document.documentElement.clientWidth,n=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,n-r+t[2]-t[0])}},en=J(),eo="data-scroll-locked",ea=function(e,t,r,n){var o=e.left,a=e.top,l=e.right,i=e.gap;return void 0===r&&(r="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(n,";\n   padding-right: ").concat(i,"px ").concat(n,";\n  }\n  body[").concat(eo,"] {\n    overflow: hidden ").concat(n,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(n,";"),"margin"===r&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(n,";\n    "),"padding"===r&&"padding-right: ".concat(i,"px ").concat(n,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(W," {\n    right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat($," {\n    margin-right: ").concat(i,"px ").concat(n,";\n  }\n  \n  .").concat(W," .").concat(W," {\n    right: 0 ").concat(n,";\n  }\n  \n  .").concat($," .").concat($," {\n    margin-right: 0 ").concat(n,";\n  }\n  \n  body[").concat(eo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},el=function(){var e=parseInt(document.body.getAttribute(eo)||"0",10);return isFinite(e)?e:0},ei=function(){l.useEffect(function(){return document.body.setAttribute(eo,(el()+1).toString()),function(){var e=el()-1;e<=0?document.body.removeAttribute(eo):document.body.setAttribute(eo,e.toString())}},[])},es=function(e){var t=e.noRelative,r=e.noImportant,n=e.gapMode,o=void 0===n?"margin":n;ei();var a=l.useMemo(function(){return er(o)},[o]);return l.createElement(en,{styles:ea(a,!t,o,r?"":"!important")})},ec=!1;if("undefined"!=typeof window)try{var eu=Object.defineProperty({},"passive",{get:function(){return ec=!0,!0}});window.addEventListener("test",eu,eu),window.removeEventListener("test",eu,eu)}catch(e){ec=!1}var ed=!!ec&&{passive:!1},ef=function(e,t){if(!(e instanceof Element))return!1;var r=window.getComputedStyle(e);return"hidden"!==r[t]&&(r.overflowY!==r.overflowX||"TEXTAREA"===e.tagName||"visible"!==r[t])},ep=function(e,t){var r=t.ownerDocument,n=t;do{if("undefined"!=typeof ShadowRoot&&n instanceof ShadowRoot&&(n=n.host),em(e,n)){var o=ev(e,n);if(o[1]>o[2])return!0}n=n.parentNode}while(n&&n!==r.body);return!1},em=function(e,t){return"v"===e?ef(t,"overflowY"):ef(t,"overflowX")},ev=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eg=function(e,t,r,n,o){var a,l=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),i=l*n,s=r.target,c=t.contains(s),u=!1,d=i>0,f=0,p=0;do{if(!s)break;var m=ev(e,s),v=m[0],g=m[1]-m[2]-l*v;(v||g)&&em(e,s)&&(f+=g,p+=v);var h=s.parentNode;s=h&&h.nodeType===Node.DOCUMENT_FRAGMENT_NODE?h.host:h}while(!c&&s!==document.body||c&&(t.contains(s)||t===s));return d&&(o&&1>Math.abs(f)||!o&&i>f)?u=!0:!d&&(o&&1>Math.abs(p)||!o&&-i>p)&&(u=!0),u},eh=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eb=function(e){return[e.deltaX,e.deltaY]},ey=function(e){return e&&"current"in e?e.current:e},ew=0,ex=[];let ek=(n=function(e){var t=l.useRef([]),r=l.useRef([0,0]),n=l.useRef(),o=l.useState(ew++)[0],a=l.useState(J)[0],i=l.useRef(e);l.useEffect(function(){i.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,r){if(r||2==arguments.length)for(var n,o=0,a=t.length;o<a;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ey),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var s=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var o,a=eh(e),l=r.current,s="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=ep(d,u);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ep(d,u)),!f)return!1;if(!n.current&&"changedTouches"in e&&(s||c)&&(n.current=o),!o)return!0;var p=n.current||o;return eg(p,t,e,"h"===p?s:c,!0)},[]),c=l.useCallback(function(e){if(ex.length&&ex[ex.length-1]===a){var r="deltaY"in e?eb(e):eh(e),n=t.current.filter(function(t){var n;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(n=t.delta,n[0]===r[0]&&n[1]===r[1])})[0];if(n&&n.should){e.cancelable&&e.preventDefault();return}if(!n){var o=(i.current.shards||[]).map(ey).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?s(e,o[0]):!i.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),u=l.useCallback(function(e,r,n,o){var a={name:e,delta:r,target:n,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(n)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=l.useCallback(function(e){r.current=eh(e),n.current=void 0},[]),f=l.useCallback(function(t){u(t.type,eb(t),t.target,s(t,e.lockRef.current))},[]),p=l.useCallback(function(t){u(t.type,eh(t),t.target,s(t,e.lockRef.current))},[]);l.useEffect(function(){return ex.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,ed),document.addEventListener("touchmove",c,ed),document.addEventListener("touchstart",d,ed),function(){ex=ex.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,ed),document.removeEventListener("touchmove",c,ed),document.removeEventListener("touchstart",d,ed)}},[]);var m=e.removeScrollBar,v=e.inert;return l.createElement(l.Fragment,null,v?l.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,m?l.createElement(es,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},q.useMedium(n),Y);var eE=l.forwardRef(function(e,t){return l.createElement(X,_({},e,{ref:t,sideCar:ek}))});eE.classNames=X.classNames;var eN=new WeakMap,eC=new WeakMap,eR={},ej=0,eA=function(e){return e&&(e.host||eA(e.parentNode))},eS=function(e,t,r,n){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var r=eA(e);return r&&t.contains(r)?r:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eR[r]||(eR[r]=new WeakMap);var a=eR[r],l=[],i=new Set,s=new Set(o),c=function(e){!e||i.has(e)||(i.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){!e||s.has(e)||Array.prototype.forEach.call(e.children,function(e){if(i.has(e))u(e);else try{var t=e.getAttribute(n),o=null!==t&&"false"!==t,s=(eN.get(e)||0)+1,c=(a.get(e)||0)+1;eN.set(e,s),a.set(e,c),l.push(e),1===s&&o&&eC.set(e,!0),1===c&&e.setAttribute(r,"true"),o||e.setAttribute(n,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return u(t),i.clear(),ej++,function(){l.forEach(function(e){var t=eN.get(e)-1,o=a.get(e)-1;eN.set(e,t),a.set(e,o),t||(eC.has(e)||e.removeAttribute(n),eC.delete(e)),o||e.removeAttribute(r)}),--ej||(eN=new WeakMap,eN=new WeakMap,eC=new WeakMap,eR={})}},eD=function(e,t,r){void 0===r&&(r="data-aria-hidden");var n=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(n.push.apply(n,Array.from(o.querySelectorAll("[aria-live], script"))),eS(n,o,r,"aria-hidden")):function(){return null}},eO=r(9708),eM="Dialog",[eP,eT]=(0,s.A)(eM),[eL,eI]=eP(eM),ez=e=>{let{__scopeDialog:t,children:r,open:n,defaultOpen:o,onOpenChange:a,modal:i=!0}=e,s=l.useRef(null),c=l.useRef(null),[u,d]=(0,v.i)({prop:n,defaultProp:null!=o&&o,onChange:a,caller:eM});return(0,b.jsx)(eL,{scope:t,triggerRef:s,contentRef:c,contentId:m(),titleId:m(),descriptionId:m(),open:u,onOpenChange:d,onOpenToggle:l.useCallback(()=>d(e=>!e),[d]),modal:i,children:r})};ez.displayName=eM;var e_="DialogTrigger",eF=l.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eI(e_,r),a=(0,c.s)(t,o.triggerRef);return(0,b.jsx)(g.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e9(o.open),...n,ref:a,onClick:(0,u.m)(e.onClick,o.onOpenToggle)})});eF.displayName=e_;var eW="DialogPortal",[e$,eG]=eP(eW,{forceMount:void 0}),eB=e=>{let{__scopeDialog:t,forceMount:r,children:n,container:o}=e,a=eI(eW,t);return(0,b.jsx)(e$,{scope:t,forceMount:r,children:l.Children.map(n,e=>(0,b.jsx)(L.C,{present:r||a.open,children:(0,b.jsx)(T,{asChild:!0,container:o,children:e})}))})};eB.displayName=eW;var eU="DialogOverlay",eK=l.forwardRef((e,t)=>{let r=eG(eU,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=eI(eU,e.__scopeDialog);return a.modal?(0,b.jsx)(L.C,{present:n||a.open,children:(0,b.jsx)(eV,{...o,ref:t})}):null});eK.displayName=eU;var eq=(0,eO.TL)("DialogOverlay.RemoveScroll"),eV=l.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eI(eU,r);return(0,b.jsx)(eE,{as:eq,allowPinchZoom:!0,shards:[o.contentRef],children:(0,b.jsx)(g.sG.div,{"data-state":e9(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),eX="DialogContent",eY=l.forwardRef((e,t)=>{let r=eG(eX,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=eI(eX,e.__scopeDialog);return(0,b.jsx)(L.C,{present:n||a.open,children:a.modal?(0,b.jsx)(eZ,{...o,ref:t}):(0,b.jsx)(eH,{...o,ref:t})})});eY.displayName=eX;var eZ=l.forwardRef((e,t)=>{let r=eI(eX,e.__scopeDialog),n=l.useRef(null),o=(0,c.s)(t,r.contentRef,n);return l.useEffect(()=>{let e=n.current;if(e)return eD(e)},[]),(0,b.jsx)(eJ,{...e,ref:o,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,u.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,u.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,u.m)(e.onFocusOutside,e=>e.preventDefault())})}),eH=l.forwardRef((e,t)=>{let r=eI(eX,e.__scopeDialog),n=l.useRef(!1),o=l.useRef(!1);return(0,b.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,l;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(n.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),n.current=!1,o.current=!1},onInteractOutside:t=>{var a,l;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(n.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eJ=l.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:n,onOpenAutoFocus:o,onCloseAutoFocus:a,...i}=e,s=eI(eX,r),u=l.useRef(null),d=(0,c.s)(t,u);return l.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:z()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:z()),I++,()=>{1===I&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),I--}},[]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(j,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,b.jsx)(x,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":e9(s.open),...i,ref:d,onDismiss:()=>s.onOpenChange(!1)})}),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(e3,{titleId:s.titleId}),(0,b.jsx)(te,{contentRef:u,descriptionId:s.descriptionId})]})]})}),eQ="DialogTitle",e0=l.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eI(eQ,r);return(0,b.jsx)(g.sG.h2,{id:o.titleId,...n,ref:t})});e0.displayName=eQ;var e1="DialogDescription",e2=l.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eI(e1,r);return(0,b.jsx)(g.sG.p,{id:o.descriptionId,...n,ref:t})});e2.displayName=e1;var e5="DialogClose",e6=l.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=eI(e5,r);return(0,b.jsx)(g.sG.button,{type:"button",...n,ref:t,onClick:(0,u.m)(e.onClick,()=>o.onOpenChange(!1))})});function e9(e){return e?"open":"closed"}e6.displayName=e5;var e4="DialogTitleWarning",[e8,e7]=(0,s.q)(e4,{contentName:eX,titleName:eQ,docsSlug:"dialog"}),e3=e=>{let{titleId:t}=e,r=e7(e4),n="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return l.useEffect(()=>{t&&(document.getElementById(t)||console.error(n))},[n,t]),null},te=e=>{let{contentRef:t,descriptionId:r}=e,n=e7("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(n.contentName,"}.");return l.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(o))},[o,t,r]),null},tt="AlertDialog",[tr,tn]=(0,s.A)(tt,[eT]),to=eT(),ta=e=>{let{__scopeAlertDialog:t,...r}=e,n=to(t);return(0,b.jsx)(ez,{...n,...r,modal:!0})};ta.displayName=tt;var tl=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=to(r);return(0,b.jsx)(eF,{...o,...n,ref:t})});tl.displayName="AlertDialogTrigger";var ti=e=>{let{__scopeAlertDialog:t,...r}=e,n=to(t);return(0,b.jsx)(eB,{...n,...r})};ti.displayName="AlertDialogPortal";var ts=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=to(r);return(0,b.jsx)(eK,{...o,...n,ref:t})});ts.displayName="AlertDialogOverlay";var tc="AlertDialogContent",[tu,td]=tr(tc),tf=(0,eO.Dc)("AlertDialogContent"),tp=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,children:n,...o}=e,a=to(r),i=l.useRef(null),s=(0,c.s)(t,i),d=l.useRef(null);return(0,b.jsx)(e8,{contentName:tc,titleName:tm,docsSlug:"alert-dialog",children:(0,b.jsx)(tu,{scope:r,cancelRef:d,children:(0,b.jsxs)(eY,{role:"alertdialog",...a,...o,ref:s,onOpenAutoFocus:(0,u.m)(o.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=d.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,b.jsx)(tf,{children:n}),(0,b.jsx)(tx,{contentRef:i})]})})})});tp.displayName=tc;var tm="AlertDialogTitle",tv=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=to(r);return(0,b.jsx)(e0,{...o,...n,ref:t})});tv.displayName=tm;var tg="AlertDialogDescription",th=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=to(r);return(0,b.jsx)(e2,{...o,...n,ref:t})});th.displayName=tg;var tb=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,o=to(r);return(0,b.jsx)(e6,{...o,...n,ref:t})});tb.displayName="AlertDialogAction";var ty="AlertDialogCancel",tw=l.forwardRef((e,t)=>{let{__scopeAlertDialog:r,...n}=e,{cancelRef:o}=td(ty,r),a=to(r),l=(0,c.s)(t,o);return(0,b.jsx)(e6,{...a,...n,ref:l})});tw.displayName=ty;var tx=e=>{let{contentRef:t}=e,r="`".concat(tc,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(tc,"` by passing a `").concat(tg,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(tc,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return l.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},tk=ta,tE=tl,tN=ti,tC=ts,tR=tp,tj=tb,tA=tw,tS=tv,tD=th},2085:(e,t,r)=>{r.d(t,{F:()=>l});var n=r(2596);let o=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,a=n.$,l=(e,t)=>r=>{var n;if((null==t?void 0:t.variants)==null)return a(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:l,defaultVariants:i}=t,s=Object.keys(l).map(e=>{let t=null==r?void 0:r[e],n=null==i?void 0:i[e];if(null===t)return null;let a=o(t)||o(n);return l[e][a]}),c=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return a(e,s,null==t||null==(n=t.compoundVariants)?void 0:n.reduce((e,t)=>{let{class:r,className:n,...o}=t;return Object.entries(o).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...i,...c}[t]):({...i,...c})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}},2525:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]])},2596:(e,t,r)=>{r.d(t,{$:()=>n});function n(){for(var e,t,r=0,n="",o=arguments.length;r<o;r++)(e=arguments[r])&&(t=function e(t){var r,n,o="";if("string"==typeof t||"number"==typeof t)o+=t;else if("object"==typeof t)if(Array.isArray(t)){var a=t.length;for(r=0;r<a;r++)t[r]&&(n=e(t[r]))&&(o&&(o+=" "),o+=n)}else for(n in t)t[n]&&(o&&(o+=" "),o+=n);return o}(e))&&(n&&(n+=" "),n+=t);return n}},2712:(e,t,r)=>{r.d(t,{N:()=>o});var n=r(2115),o=globalThis?.document?n.useLayoutEffect:()=>{}},3655:(e,t,r)=>{r.d(t,{hO:()=>s,sG:()=>i});var n=r(2115),o=r(7650),a=r(9708),l=r(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,a.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(o?r:t,{...a,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function s(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5185:(e,t,r)=>{r.d(t,{m:()=>n});function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5845:(e,t,r)=>{r.d(t,{i:()=>i});var n,o=r(2115),a=r(2712),l=(n||(n=r.t(o,2)))[" useInsertionEffect ".trim().toString()]||a.N;function i({prop:e,defaultProp:t,onChange:r=()=>{},caller:n}){let[a,i,s]=function({defaultProp:e,onChange:t}){let[r,n]=o.useState(e),a=o.useRef(r),i=o.useRef(t);return l(()=>{i.current=t},[t]),o.useEffect(()=>{a.current!==r&&(i.current?.(r),a.current=r)},[r,a]),[r,n,i]}({defaultProp:t,onChange:r}),c=void 0!==e,u=c?e:a;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==c){let t=c?"controlled":"uncontrolled";console.warn(`${n} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=c},[c,n])}return[u,o.useCallback(t=>{if(c){let r="function"==typeof t?t(e):t;r!==e&&s.current?.(r)}else i(t)},[c,e,i,s])]}Symbol("RADIX:SYNC_STATE")},6081:(e,t,r)=>{r.d(t,{A:()=>l,q:()=>a});var n=r(2115),o=r(5155);function a(e,t){let r=n.createContext(t),a=e=>{let{children:t,...a}=e,l=n.useMemo(()=>a,Object.values(a));return(0,o.jsx)(r.Provider,{value:l,children:t})};return a.displayName=e+"Provider",[a,function(o){let a=n.useContext(r);if(a)return a;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function l(e,t=[]){let r=[],a=()=>{let t=r.map(e=>n.createContext(e));return function(r){let o=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:o}}),[r,o])}};return a.scopeName=e,[function(t,a){let l=n.createContext(a),i=r.length;r=[...r,a];let s=t=>{let{scope:r,children:a,...s}=t,c=r?.[e]?.[i]||l,u=n.useMemo(()=>s,Object.values(s));return(0,o.jsx)(c.Provider,{value:u,children:a})};return s.displayName=t+"Provider",[s,function(r,o){let s=o?.[e]?.[i]||l,c=n.useContext(s);if(c)return c;if(void 0!==a)return a;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=r.reduce((t,{useScope:r,scopeName:n})=>{let o=r(e)[`__scope${n}`];return{...t,...o}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return r.scopeName=t.scopeName,r}(a,...t)]}},6101:(e,t,r)=>{r.d(t,{s:()=>l,t:()=>a});var n=r(2115);function o(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let r=!1,n=e.map(e=>{let n=o(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():o(e[t],null)}}}}function l(...e){return n.useCallback(a(...e),e)}},7484:(e,t,r)=>{r.d(t,{C1:()=>k,bL:()=>w});var n=r(2115),o=r(6101),a=r(6081),l=r(5185),i=r(5845),s=r(2712),c=r(8905),u=r(3655),d=r(5155),f="Checkbox",[p,m]=(0,a.A)(f),[v,g]=p(f);function h(e){let{__scopeCheckbox:t,checked:r,children:o,defaultChecked:a,disabled:l,form:s,name:c,onCheckedChange:u,required:p,value:m="on",internal_do_not_use_render:g}=e,[h,b]=(0,i.i)({prop:r,defaultProp:null!=a&&a,onChange:u,caller:f}),[y,w]=n.useState(null),[x,k]=n.useState(null),E=n.useRef(!1),N=!y||!!s||!!y.closest("form"),R={checked:h,disabled:l,setChecked:b,control:y,setControl:w,name:c,form:s,value:m,hasConsumerStoppedPropagationRef:E,required:p,defaultChecked:!C(a)&&a,isFormControl:N,bubbleInput:x,setBubbleInput:k};return(0,d.jsx)(v,{scope:t,...R,children:"function"==typeof g?g(R):o})}var b="CheckboxTrigger",y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:a,onClick:i,...s}=e,{control:c,value:f,disabled:p,checked:m,required:v,setControl:h,setChecked:y,hasConsumerStoppedPropagationRef:w,isFormControl:x,bubbleInput:k}=g(b,r),E=(0,o.s)(t,h),N=n.useRef(m);return n.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>y(N.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,y]),(0,d.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":C(m)?"mixed":m,"aria-required":v,"data-state":R(m),"data-disabled":p?"":void 0,disabled:p,value:f,...s,ref:E,onKeyDown:(0,l.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,l.m)(i,e=>{y(e=>!!C(e)||!e),k&&x&&(w.current=e.isPropagationStopped(),w.current||e.stopPropagation())})})});y.displayName=b;var w=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:o,defaultChecked:a,required:l,disabled:i,value:s,onCheckedChange:c,form:u,...f}=e;return(0,d.jsx)(h,{__scopeCheckbox:r,checked:o,defaultChecked:a,disabled:i,required:l,onCheckedChange:c,name:n,form:u,value:s,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(y,{...f,ref:t,__scopeCheckbox:r}),n&&(0,d.jsx)(N,{__scopeCheckbox:r})]})}})});w.displayName=f;var x="CheckboxIndicator",k=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...o}=e,a=g(x,r);return(0,d.jsx)(c.C,{present:n||C(a.checked)||!0===a.checked,children:(0,d.jsx)(u.sG.span,{"data-state":R(a.checked),"data-disabled":a.disabled?"":void 0,...o,ref:t,style:{pointerEvents:"none",...e.style}})})});k.displayName=x;var E="CheckboxBubbleInput",N=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...a}=e,{control:l,hasConsumerStoppedPropagationRef:i,checked:c,defaultChecked:f,required:p,disabled:m,name:v,value:h,form:b,bubbleInput:y,setBubbleInput:w}=g(E,r),x=(0,o.s)(t,w),k=function(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(c),N=function(e){let[t,r]=n.useState(void 0);return(0,s.N)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let a=t[0];if("borderBoxSize"in a){let e=a.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),t}(l);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(k!==c&&e){let r=new Event("click",{bubbles:t});y.indeterminate=C(c),e.call(y,!C(c)&&c),y.dispatchEvent(r)}},[y,k,c,i]);let R=n.useRef(!C(c)&&c);return(0,d.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:R.current,required:p,disabled:m,name:v,value:h,form:b,...a,tabIndex:-1,ref:x,style:{...a.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function C(e){return"indeterminate"===e}function R(e){return C(e)?"indeterminate":e?"checked":"unchecked"}N.displayName=E},8905:(e,t,r)=>{r.d(t,{C:()=>l});var n=r(2115),o=r(6101),a=r(2712),l=e=>{let{present:t,children:r}=e,l=function(e){var t,r;let[o,l]=n.useState(),s=n.useRef(null),c=n.useRef(e),u=n.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=i(s.current);u.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=s.current,r=c.current;if(r!==e){let n=u.current,o=i(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&n!==o?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,r=null!=(e=o.ownerDocument.defaultView)?e:window,n=e=>{let n=i(s.current).includes(e.animationName);if(e.target===o&&n&&(f("ANIMATION_END"),!c.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(u.current=i(s.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",n),o.addEventListener("animationend",n),()=>{r.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",n),o.removeEventListener("animationend",n)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{s.current=e?getComputedStyle(e):null,l(e)},[])}}(t),s="function"==typeof r?r({present:l.isPresent}):n.Children.only(r),c=(0,o.s)(l.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=n&&"isReactWarning"in n&&n.isReactWarning;return o?e.ref:(o=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof r||l.isPresent?n.cloneElement(s,{ref:c}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}l.displayName="Presence"},9688:(e,t,r)=>{r.d(t,{QP:()=>ee});let n=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),a=o?n(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;let l=e.join("-");return t.validators.find(({validator:e})=>e(l))?.classGroupId},o=/^\[(.+)\]$/,a=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:l(t,e)).classGroupId=r;return}if("function"==typeof e)return i(e)?void a(e(n),t,r,n):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(([e,o])=>{a(o,l(t,e),r,n)})})},l=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},i=e=>e.isThemeGetter,s=/\s+/;function c(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=u(e))&&(n&&(n+=" "),n+=t);return n}let u=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=u(e[n]))&&(r&&(r+=" "),r+=t);return r},d=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},f=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,p=/^\((?:(\w[\w-]*):)?(.+)\)$/i,m=/^\d+\/\d+$/,v=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,g=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,h=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,b=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,y=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,w=e=>m.test(e),x=e=>!!e&&!Number.isNaN(Number(e)),k=e=>!!e&&Number.isInteger(Number(e)),E=e=>e.endsWith("%")&&x(e.slice(0,-1)),N=e=>v.test(e),C=()=>!0,R=e=>g.test(e)&&!h.test(e),j=()=>!1,A=e=>b.test(e),S=e=>y.test(e),D=e=>!M(e)&&!_(e),O=e=>K(e,Y,j),M=e=>f.test(e),P=e=>K(e,Z,R),T=e=>K(e,H,x),L=e=>K(e,V,j),I=e=>K(e,X,S),z=e=>K(e,Q,A),_=e=>p.test(e),F=e=>q(e,Z),W=e=>q(e,J),$=e=>q(e,V),G=e=>q(e,Y),B=e=>q(e,X),U=e=>q(e,Q,!0),K=(e,t,r)=>{let n=f.exec(e);return!!n&&(n[1]?t(n[1]):r(n[2]))},q=(e,t,r=!1)=>{let n=p.exec(e);return!!n&&(n[1]?t(n[1]):r)},V=e=>"position"===e||"percentage"===e,X=e=>"image"===e||"url"===e,Y=e=>"length"===e||"size"===e||"bg-size"===e,Z=e=>"length"===e,H=e=>"number"===e,J=e=>"family-name"===e,Q=e=>"shadow"===e;Symbol.toStringTag;let ee=function(e,...t){let r,l,i,u=function(s){let c;return l=(r={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,o=(o,a)=>{r.set(o,a),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(o(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):o(e,t)}}})((c=t.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,n=e=>{let t,r,n=[],o=0,a=0,l=0;for(let r=0;r<e.length;r++){let i=e[r];if(0===o&&0===a){if(":"===i){n.push(e.slice(l,r)),l=r+1;continue}if("/"===i){t=r;continue}}"["===i?o++:"]"===i?o--:"("===i?a++:")"===i&&a--}let i=0===n.length?e:e.substring(l),s=(r=i).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:n,hasImportantModifier:s!==i,baseClassName:s,maybePostfixModifierPosition:t&&t>l?t-l:void 0}};if(t){let e=t+":",r=n;n=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=n;n=t=>r({className:t,parseClassName:e})}return n})(c),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],n=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...n.sort(),e),n=[]):n.push(e)}),r.push(...n.sort()),r}})(c),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,n={nextPart:new Map,validators:[]};for(let e in r)a(r[e],n,e,t);return n})(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:l}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),n(r,t)||(e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let n=r[e]||[];return t&&l[e]?[...n,...l[e]]:n}}})(c)}).cache.get,i=r.cache.set,u=d,d(s)};function d(e){let t=l(e);if(t)return t;let n=((e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:o,sortModifiers:a}=t,l=[],i=e.trim().split(s),c="";for(let e=i.length-1;e>=0;e-=1){let t=i[e],{isExternal:s,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(s){c=t+(c.length>0?" "+c:c);continue}let m=!!p,v=n(m?f.substring(0,p):f);if(!v){if(!m||!(v=n(f))){c=t+(c.length>0?" "+c:c);continue}m=!1}let g=a(u).join(":"),h=d?g+"!":g,b=h+v;if(l.includes(b))continue;l.push(b);let y=o(v,m);for(let e=0;e<y.length;++e){let t=y[e];l.push(h+t)}c=t+(c.length>0?" "+c:c)}return c})(e,r);return i(e,n),n}return function(){return u(c.apply(null,arguments))}}(()=>{let e=d("color"),t=d("font"),r=d("text"),n=d("font-weight"),o=d("tracking"),a=d("leading"),l=d("breakpoint"),i=d("container"),s=d("spacing"),c=d("radius"),u=d("shadow"),f=d("inset-shadow"),p=d("text-shadow"),m=d("drop-shadow"),v=d("blur"),g=d("perspective"),h=d("aspect"),b=d("ease"),y=d("animate"),R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],j=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],A=()=>[...j(),_,M],S=()=>["auto","hidden","clip","visible","scroll"],K=()=>["auto","contain","none"],q=()=>[_,M,s],V=()=>[w,"full","auto",...q()],X=()=>[k,"none","subgrid",_,M],Y=()=>["auto",{span:["full",k,_,M]},k,_,M],Z=()=>[k,"auto",_,M],H=()=>["auto","min","max","fr",_,M],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...q()],et=()=>[w,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...q()],er=()=>[e,_,M],en=()=>[...j(),$,L,{position:[_,M]}],eo=()=>["no-repeat",{repeat:["","x","y","space","round"]}],ea=()=>["auto","cover","contain",G,O,{size:[_,M]}],el=()=>[E,F,P],ei=()=>["","none","full",c,_,M],es=()=>["",x,F,P],ec=()=>["solid","dashed","dotted","double"],eu=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],ed=()=>[x,E,$,L],ef=()=>["","none",v,_,M],ep=()=>["none",x,_,M],em=()=>["none",x,_,M],ev=()=>[x,_,M],eg=()=>[w,"full",...q()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[N],breakpoint:[N],color:[C],container:[N],"drop-shadow":[N],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[N],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[N],shadow:[N],spacing:["px",x],text:[N],"text-shadow":[N],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",w,M,_,h]}],container:["container"],columns:[{columns:[x,M,_,i]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:A()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:K()}],"overscroll-x":[{"overscroll-x":K()}],"overscroll-y":[{"overscroll-y":K()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[k,"auto",_,M]}],basis:[{basis:[w,"full","auto",i,...q()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[x,w,"auto","initial","none",M]}],grow:[{grow:["",x,_,M]}],shrink:[{shrink:["",x,_,M]}],order:[{order:[k,"first","last","none",_,M]}],"grid-cols":[{"grid-cols":X()}],"col-start-end":[{col:Y()}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":X()}],"row-start-end":[{row:Y()}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":H()}],"auto-rows":[{"auto-rows":H()}],gap:[{gap:q()}],"gap-x":[{"gap-x":q()}],"gap-y":[{"gap-y":q()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:q()}],px:[{px:q()}],py:[{py:q()}],ps:[{ps:q()}],pe:[{pe:q()}],pt:[{pt:q()}],pr:[{pr:q()}],pb:[{pb:q()}],pl:[{pl:q()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":q()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":q()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[i,"screen",...et()]}],"min-w":[{"min-w":[i,"screen","none",...et()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[l]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,F,P]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[n,_,T]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",E,M]}],"font-family":[{font:[W,M,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,_,M]}],"line-clamp":[{"line-clamp":[x,"none",_,T]}],leading:[{leading:[a,...q()]}],"list-image":[{"list-image":["none",_,M]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",_,M]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[x,"from-font","auto",_,P]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[x,"auto",_,M]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:q()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",_,M]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",_,M]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:en()}],"bg-repeat":[{bg:eo()}],"bg-size":[{bg:ea()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},k,_,M],radial:["",_,M],conic:[k,_,M]},B,I]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:el()}],"gradient-via-pos":[{via:el()}],"gradient-to-pos":[{to:el()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ei()}],"rounded-s":[{"rounded-s":ei()}],"rounded-e":[{"rounded-e":ei()}],"rounded-t":[{"rounded-t":ei()}],"rounded-r":[{"rounded-r":ei()}],"rounded-b":[{"rounded-b":ei()}],"rounded-l":[{"rounded-l":ei()}],"rounded-ss":[{"rounded-ss":ei()}],"rounded-se":[{"rounded-se":ei()}],"rounded-ee":[{"rounded-ee":ei()}],"rounded-es":[{"rounded-es":ei()}],"rounded-tl":[{"rounded-tl":ei()}],"rounded-tr":[{"rounded-tr":ei()}],"rounded-br":[{"rounded-br":ei()}],"rounded-bl":[{"rounded-bl":ei()}],"border-w":[{border:es()}],"border-w-x":[{"border-x":es()}],"border-w-y":[{"border-y":es()}],"border-w-s":[{"border-s":es()}],"border-w-e":[{"border-e":es()}],"border-w-t":[{"border-t":es()}],"border-w-r":[{"border-r":es()}],"border-w-b":[{"border-b":es()}],"border-w-l":[{"border-l":es()}],"divide-x":[{"divide-x":es()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":es()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[x,_,M]}],"outline-w":[{outline:["",x,F,P]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,U,z]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",f,U,z]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:es()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[x,P]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":es()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",p,U,z]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[x,_,M]}],"mix-blend":[{"mix-blend":[...eu(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":eu()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[x]}],"mask-image-linear-from-pos":[{"mask-linear-from":ed()}],"mask-image-linear-to-pos":[{"mask-linear-to":ed()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":ed()}],"mask-image-t-to-pos":[{"mask-t-to":ed()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":ed()}],"mask-image-r-to-pos":[{"mask-r-to":ed()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":ed()}],"mask-image-b-to-pos":[{"mask-b-to":ed()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":ed()}],"mask-image-l-to-pos":[{"mask-l-to":ed()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":ed()}],"mask-image-x-to-pos":[{"mask-x-to":ed()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":ed()}],"mask-image-y-to-pos":[{"mask-y-to":ed()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[_,M]}],"mask-image-radial-from-pos":[{"mask-radial-from":ed()}],"mask-image-radial-to-pos":[{"mask-radial-to":ed()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":j()}],"mask-image-conic-pos":[{"mask-conic":[x]}],"mask-image-conic-from-pos":[{"mask-conic-from":ed()}],"mask-image-conic-to-pos":[{"mask-conic-to":ed()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:en()}],"mask-repeat":[{mask:eo()}],"mask-size":[{mask:ea()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",_,M]}],filter:[{filter:["","none",_,M]}],blur:[{blur:ef()}],brightness:[{brightness:[x,_,M]}],contrast:[{contrast:[x,_,M]}],"drop-shadow":[{"drop-shadow":["","none",m,U,z]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",x,_,M]}],"hue-rotate":[{"hue-rotate":[x,_,M]}],invert:[{invert:["",x,_,M]}],saturate:[{saturate:[x,_,M]}],sepia:[{sepia:["",x,_,M]}],"backdrop-filter":[{"backdrop-filter":["","none",_,M]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[x,_,M]}],"backdrop-contrast":[{"backdrop-contrast":[x,_,M]}],"backdrop-grayscale":[{"backdrop-grayscale":["",x,_,M]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[x,_,M]}],"backdrop-invert":[{"backdrop-invert":["",x,_,M]}],"backdrop-opacity":[{"backdrop-opacity":[x,_,M]}],"backdrop-saturate":[{"backdrop-saturate":[x,_,M]}],"backdrop-sepia":[{"backdrop-sepia":["",x,_,M]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":q()}],"border-spacing-x":[{"border-spacing-x":q()}],"border-spacing-y":[{"border-spacing-y":q()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",_,M]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[x,"initial",_,M]}],ease:[{ease:["linear","initial",b,_,M]}],delay:[{delay:[x,_,M]}],animate:[{animate:["none",y,_,M]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[g,_,M]}],"perspective-origin":[{"perspective-origin":A()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ev()}],"skew-x":[{"skew-x":ev()}],"skew-y":[{"skew-y":ev()}],transform:[{transform:[_,M,"","none","gpu","cpu"]}],"transform-origin":[{origin:A()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eg()}],"translate-x":[{"translate-x":eg()}],"translate-y":[{"translate-y":eg()}],"translate-z":[{"translate-z":eg()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",_,M]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":q()}],"scroll-mx":[{"scroll-mx":q()}],"scroll-my":[{"scroll-my":q()}],"scroll-ms":[{"scroll-ms":q()}],"scroll-me":[{"scroll-me":q()}],"scroll-mt":[{"scroll-mt":q()}],"scroll-mr":[{"scroll-mr":q()}],"scroll-mb":[{"scroll-mb":q()}],"scroll-ml":[{"scroll-ml":q()}],"scroll-p":[{"scroll-p":q()}],"scroll-px":[{"scroll-px":q()}],"scroll-py":[{"scroll-py":q()}],"scroll-ps":[{"scroll-ps":q()}],"scroll-pe":[{"scroll-pe":q()}],"scroll-pt":[{"scroll-pt":q()}],"scroll-pr":[{"scroll-pr":q()}],"scroll-pb":[{"scroll-pb":q()}],"scroll-pl":[{"scroll-pl":q()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",_,M]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[x,F,P,T]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})},9708:(e,t,r)=>{r.d(t,{DX:()=>i,Dc:()=>c,TL:()=>l});var n=r(2115),o=r(6101),a=r(5155);function l(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...a}=e;if(n.isValidElement(r)){var l;let e,i,s=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),c=function(e,t){let r={...t};for(let n in t){let o=e[n],a=t[n];/^on[A-Z]/.test(n)?o&&a?r[n]=(...e)=>{let t=a(...e);return o(...e),t}:o&&(r[n]=o):"style"===n?r[n]={...o,...a}:"className"===n&&(r[n]=[o,a].filter(Boolean).join(" "))}return{...e,...r}}(a,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,o.t)(t,s):s),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:o,...l}=e,i=n.Children.toArray(o),s=i.find(u);if(s){let e=s.props.children,o=i.map(t=>t!==s?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,o):null})}return(0,a.jsx)(t,{...l,ref:r,children:o})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),s=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=s,t}function u(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===s}},9946:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:c="",children:u,iconNode:d,...f}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:s?24*Number(i)/Number(o):i,className:a("lucide",c),...!u&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(u)?u:[u]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:s,...c}=r;return(0,n.createElement)(i,{ref:l,iconNode:t,className:a("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),s),...c})});return r.displayName=o(e),r}}}]);