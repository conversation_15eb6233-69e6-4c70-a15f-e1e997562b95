"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_TodoList_tsx",{

/***/ "(app-pages-browser)/./src/components/TodoList.tsx":
/*!*************************************!*\
  !*** ./src/components/TodoList.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* harmony import */ var _AddTodo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AddTodo */ \"(app-pages-browser)/./src/components/AddTodo.tsx\");\n/* harmony import */ var _TodoItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TodoItem */ \"(app-pages-browser)/./src/components/TodoItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TodoList() {\n    _s();\n    const [todos, setTodos, isLoaded] = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__.useLocalStorage)('mytodos', []);\n    // 添加新任务\n    const addTodo = (title)=>{\n        // 使用更安全的 ID 生成方式，避免 hydration 错误\n        const timestamp = Date.now();\n        const randomSuffix = Math.random().toString(36).substr(2, 9);\n        const newTodo = {\n            id: \"\".concat(timestamp, \"-\").concat(randomSuffix),\n            title,\n            status: 'pending',\n            createdAt: timestamp\n        };\n        setTodos((prevTodos)=>[\n                ...prevTodos,\n                newTodo\n            ]);\n    };\n    // 切换任务状态\n    const toggleTodo = (id)=>{\n        setTodos((prevTodos)=>prevTodos.map((todo)=>todo.id === id ? {\n                    ...todo,\n                    status: todo.status === 'pending' ? 'completed' : 'pending'\n                } : todo));\n    };\n    // 删除任务\n    const deleteTodo = (id)=>{\n        setTodos((prevTodos)=>prevTodos.filter((todo)=>todo.id !== id));\n    };\n    // 在数据加载完成前显示加载状态，避免水合错误\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl mx-auto space-y-8 animate-fade-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4 animate-slide-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold tracking-tight text-foreground\",\n                            children: \"MyTodos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 49,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium text-muted-foreground leading-relaxed\",\n                            children: \"管理您的待办事项，提高工作效率\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 52,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 mx-auto border-4 border-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-sm text-muted-foreground\",\n                            children: \"加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-2xl mx-auto space-y-8 animate-fade-in\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4 animate-slide-up\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold tracking-tight text-foreground\",\n                        children: \"MyTodos\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-muted-foreground leading-relaxed\",\n                        children: \"管理您的待办事项，提高工作效率\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 71,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddTodo__WEBPACK_IMPORTED_MODULE_2__.AddTodo, {\n                    onAdd: addTodo\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 78,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: todos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center animate-bounce-subtle\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-muted-foreground\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                        lineNumber: 88,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: \"还没有任务\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 91,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground max-w-sm mx-auto leading-relaxed\",\n                                children: \"创建第一个待办，开始提升效率吧！\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 85,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 84,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: todos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TodoItem__WEBPACK_IMPORTED_MODULE_3__.TodoItem, {\n                            todo: todo,\n                            onToggle: toggleTodo,\n                            onDelete: deleteTodo\n                        }, todo.id, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 98,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, this),\n            todos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-base font-medium text-muted-foreground\",\n                    children: [\n                        \"总共 \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-foreground\",\n                            children: todos.length\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 115,\n                            columnNumber: 16\n                        }, this),\n                        \" 个任务， 已完成 \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-primary\",\n                            children: todos.filter((todo)=>todo.status === 'completed').length\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 17\n                        }, this),\n                        \" 个\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 113,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n        lineNumber: 65,\n        columnNumber: 5\n    }, this);\n}\n_s(TodoList, \"Df9g84I/FFa91/q2yr6ylim6gY8=\", false, function() {\n    return [\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__.useLocalStorage\n    ];\n});\n_c = TodoList;\nvar _c;\n$RefreshReg$(_c, \"TodoList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TodoList.tsx\n"));

/***/ })

});