"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("_app-pages-browser_src_components_TodoList_tsx",{

/***/ "(app-pages-browser)/./src/components/TodoList.tsx":
/*!*************************************!*\
  !*** ./src/components/TodoList.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TodoList: () => (/* binding */ TodoList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/hooks/useLocalStorage */ \"(app-pages-browser)/./src/hooks/useLocalStorage.ts\");\n/* harmony import */ var _AddTodo__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./AddTodo */ \"(app-pages-browser)/./src/components/AddTodo.tsx\");\n/* harmony import */ var _TodoItem__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TodoItem */ \"(app-pages-browser)/./src/components/TodoItem.tsx\");\n/* __next_internal_client_entry_do_not_use__ TodoList auto */ \nvar _s = $RefreshSig$();\n\n\n\nfunction TodoList() {\n    _s();\n    const [todos, setTodos, isLoaded] = (0,_hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__.useLocalStorage)('mytodos', []);\n    // 添加新任务\n    const addTodo = (title)=>{\n        // 使用客户端安全的 ID 生成方式，避免 hydration 错误\n        // 只在客户端执行时生成 ID 和时间戳\n        const generateId = ()=>{\n            if (true) {\n                // 在浏览器环境中使用 crypto.randomUUID 或回退方案\n                if (crypto && crypto.randomUUID) {\n                    return crypto.randomUUID();\n                }\n                // 回退方案：使用时间戳 + 随机数\n                return \"\".concat(Date.now(), \"-\").concat(Math.random().toString(36).substr(2, 9));\n            }\n            // 服务器端返回临时 ID（实际不会被使用，因为组件使用了 ssr: false）\n            return 'temp-id';\n        };\n        const newTodo = {\n            id: generateId(),\n            title,\n            status: 'pending',\n            createdAt:  true ? Date.now() : 0\n        };\n        setTodos((prevTodos)=>[\n                ...prevTodos,\n                newTodo\n            ]);\n    };\n    // 切换任务状态\n    const toggleTodo = (id)=>{\n        setTodos((prevTodos)=>prevTodos.map((todo)=>todo.id === id ? {\n                    ...todo,\n                    status: todo.status === 'pending' ? 'completed' : 'pending'\n                } : todo));\n    };\n    // 删除任务\n    const deleteTodo = (id)=>{\n        setTodos((prevTodos)=>prevTodos.filter((todo)=>todo.id !== id));\n    };\n    // 在数据加载完成前显示加载状态，避免水合错误\n    if (!isLoaded) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full max-w-2xl mx-auto space-y-8 animate-fade-in\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center space-y-4 animate-slide-up\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-4xl font-bold tracking-tight text-foreground\",\n                            children: \"MyTodos\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg font-medium text-muted-foreground leading-relaxed\",\n                            children: \"管理您的待办事项，提高工作效率\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 mx-auto border-4 border-primary border-t-transparent rounded-full animate-spin\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"mt-4 text-sm text-muted-foreground\",\n                            children: \"加载中...\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 70,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 68,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full max-w-2xl mx-auto space-y-8 animate-fade-in\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center space-y-4 animate-slide-up\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold tracking-tight text-foreground\",\n                        children: \"MyTodos\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 80,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-medium text-muted-foreground leading-relaxed\",\n                        children: \"管理您的待办事项，提高工作效率\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 79,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_AddTodo__WEBPACK_IMPORTED_MODULE_2__.AddTodo, {\n                    onAdd: addTodo\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 90,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 89,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-4\",\n                children: todos.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-16 animate-fade-in\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center animate-bounce-subtle\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-muted-foreground\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 1.5,\n                                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                    lineNumber: 99,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-lg font-semibold text-foreground\",\n                                children: \"还没有任务\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 103,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground max-w-sm mx-auto leading-relaxed\",\n                                children: \"创建第一个待办，开始提升效率吧！\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                                lineNumber: 104,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-3\",\n                    children: todos.map((todo)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TodoItem__WEBPACK_IMPORTED_MODULE_3__.TodoItem, {\n                            todo: todo,\n                            onToggle: toggleTodo,\n                            onDelete: deleteTodo\n                        }, todo.id, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 112,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 94,\n                columnNumber: 7\n            }, this),\n            todos.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-base font-medium text-muted-foreground\",\n                    children: [\n                        \"总共 \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-foreground\",\n                            children: todos.length\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 127,\n                            columnNumber: 16\n                        }, this),\n                        \" 个任务， 已完成 \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-semibold text-primary\",\n                            children: todos.filter((todo)=>todo.status === 'completed').length\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 17\n                        }, this),\n                        \" 个\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n                lineNumber: 125,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx\",\n        lineNumber: 77,\n        columnNumber: 5\n    }, this);\n}\n_s(TodoList, \"Df9g84I/FFa91/q2yr6ylim6gY8=\", false, function() {\n    return [\n        _hooks_useLocalStorage__WEBPACK_IMPORTED_MODULE_1__.useLocalStorage\n    ];\n});\n_c = TodoList;\nvar _c;\n$RefreshReg$(_c, \"TodoList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TodoList.tsx\n"));

/***/ })

});