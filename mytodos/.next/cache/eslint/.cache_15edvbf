[{"/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/__tests__/TodoList.test.tsx": "1", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/app/layout.tsx": "2", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/app/page.tsx": "3", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/AddTodo.tsx": "4", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoItem.tsx": "5", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx": "6", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/alert-dialog.tsx": "7", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/button.tsx": "8", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/card.tsx": "9", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/checkbox.tsx": "10", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/input.tsx": "11", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/hooks/useLocalStorage.ts": "12", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/lib/utils.ts": "13", "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/types/index.ts": "14"}, {"size": 1683, "mtime": 1754066430061, "results": "15", "hashOfConfig": "16"}, {"size": 775, "mtime": 1754063595139, "results": "17", "hashOfConfig": "16"}, {"size": 1180, "mtime": 1754066713286, "results": "18", "hashOfConfig": "16"}, {"size": 1399, "mtime": 1754063543280, "results": "19", "hashOfConfig": "16"}, {"size": 3142, "mtime": 1754063569306, "results": "20", "hashOfConfig": "16"}, {"size": 4224, "mtime": 1754066595859, "results": "21", "hashOfConfig": "16"}, {"size": 3864, "mtime": 1754062815126, "results": "22", "hashOfConfig": "16"}, {"size": 2123, "mtime": 1754062815103, "results": "23", "hashOfConfig": "16"}, {"size": 1989, "mtime": 1754062815120, "results": "24", "hashOfConfig": "16"}, {"size": 1226, "mtime": 1754062815117, "results": "25", "hashOfConfig": "16"}, {"size": 967, "mtime": 1754062815113, "results": "26", "hashOfConfig": "16"}, {"size": 1612, "mtime": 1754066567810, "results": "27", "hashOfConfig": "16"}, {"size": 166, "mtime": 1754062804551, "results": "28", "hashOfConfig": "16"}, {"size": 248, "mtime": 1754062833131, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1cba7lf", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/__tests__/TodoList.test.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/app/layout.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/app/page.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/AddTodo.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoItem.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/TodoList.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/alert-dialog.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/button.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/card.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/checkbox.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/components/ui/input.tsx", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/hooks/useLocalStorage.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/lib/utils.ts", [], [], "/Users/<USER>/Documents/workspace/workspace_vscode/0801test/mytodos/src/types/index.ts", [], []]