# MyTodos 应用演示

## 功能演示步骤

### 1. 启动应用
```bash
npm run dev
```
访问 http://localhost:3000

### 2. 添加任务
- 在输入框中输入 "学习 React"
- 点击 "添加" 按钮或按回车键
- 任务会立即出现在列表中

### 3. 标记任务完成
- 点击任务前的复选框
- 任务标题会显示删除线效果
- 再次点击可以取消完成状态

### 4. 删除任务
- 点击任务右侧的垃圾桶图标
- 会弹出确认对话框
- 点击 "删除" 确认删除任务

### 5. 数据持久化验证
- 添加几个任务
- 刷新页面
- 所有任务都会保持不变（存储在 localStorage 中）

### 6. 响应式设计验证
- 调整浏览器窗口大小
- 或在移动设备上访问
- 界面会自动适应不同屏幕尺寸

## 测试验证

运行自动化测试：
```bash
npm test
```

测试覆盖：
- ✅ 渲染标题和描述
- ✅ 显示空状态提示
- ✅ 添加新任务功能
- ✅ 输入为空时禁用添加按钮

## 技术亮点

1. **现代化技术栈**: Next.js 15 + TypeScript + Tailwind CSS
2. **优雅的 UI**: 使用 shadcn/ui 组件库
3. **本地存储**: 无需后端，数据存储在浏览器中
4. **类型安全**: 完整的 TypeScript 类型定义
5. **测试覆盖**: Jest + React Testing Library
6. **响应式设计**: 适配各种设备屏幕
