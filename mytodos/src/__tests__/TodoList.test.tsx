import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { TodoList } from '@/components/TodoList';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock as Storage;

describe('TodoList', () => {
  beforeEach(() => {
    localStorageMock.getItem.mockReturnValue(null);
    localStorageMock.setItem.mockClear();
  });

  it('renders the todo list with title', () => {
    render(<TodoList />);
    
    expect(screen.getByText('MyTodos')).toBeInTheDocument();
    expect(screen.getByText('管理您的待办事项，提高工作效率')).toBeInTheDocument();
  });

  it('shows empty state when no todos', () => {
    render(<TodoList />);

    expect(screen.getByText('还没有任务')).toBeInTheDocument();
    expect(screen.getByText('创建第一个待办，开始提升效率吧！')).toBeInTheDocument();
  });

  it('can add a new todo', async () => {
    render(<TodoList />);

    const input = screen.getByPlaceholderText('添加新任务...');
    const addButton = screen.getByText('添加');

    fireEvent.change(input, { target: { value: '测试任务' } });
    fireEvent.click(addButton);

    expect(screen.getByText('测试任务')).toBeInTheDocument();
    // Check that the empty state message is no longer visible
    expect(screen.queryByText('还没有任务')).not.toBeInTheDocument();
  });

  it('disables add button when input is empty', () => {
    render(<TodoList />);
    
    const addButton = screen.getByText('添加');
    expect(addButton).toBeDisabled();
  });
});
