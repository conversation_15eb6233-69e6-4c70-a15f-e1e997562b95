import { useState, useEffect } from 'react';

/**
 * 自定义 Hook 用于管理 localStorage 中的数据
 * @param key localStorage 的键名
 * @param initialValue 初始值
 * @returns [value, setValue, isLoaded] 元组，isLoaded 表示是否已从 localStorage 加载数据
 */
export function useLocalStorage<T>(key: string, initialValue: T) {
  // 使用 initialValue 作为初始状态，避免水合错误
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const [isLoaded, setIsLoaded] = useState(false);

  // 设置值的函数
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // 允许传入函数来更新值
      const valueToStore = value instanceof Function ? value(storedValue) : value;

      setStoredValue(valueToStore);

      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined') {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.error(`Error setting localStorage key "${key}":`, error);
    }
  };

  // 在组件挂载时从 localStorage 读取数据
  useEffect(() => {
    try {
      // 检查是否在浏览器环境中
      if (typeof window !== 'undefined') {
        const item = window.localStorage.getItem(key);
        if (item) {
          const storedData = JSON.parse(item);
          setStoredValue(storedData);
        }
      }
    } catch (error) {
      console.error(`Error reading localStorage key "${key}":`, error);
    } finally {
      setIsLoaded(true);
    }
  }, [key]);

  return [storedValue, setValue, isLoaded] as const;
}
