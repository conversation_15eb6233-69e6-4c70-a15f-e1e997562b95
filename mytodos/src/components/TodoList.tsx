'use client';

import { useLocalStorage } from '@/hooks/useLocalStorage';
import { Todo } from '@/types';
import { AddTodo } from './AddTodo';
import { TodoItem } from './TodoItem';

export function TodoList() {
  const [todos, setTodos, isLoaded] = useLocalStorage<Todo[]>('mytodos', []);

  // 添加新任务
  const addTodo = (title: string) => {
    // 使用客户端安全的 ID 生成方式，避免 hydration 错误
    // 只在客户端执行时生成 ID 和时间戳
    const generateId = () => {
      if (typeof window !== 'undefined') {
        // 在浏览器环境中使用 crypto.randomUUID 或回退方案
        if (crypto && crypto.randomUUID) {
          return crypto.randomUUID();
        }
        // 回退方案：使用时间戳 + 随机数
        return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
      }
      // 服务器端返回临时 ID（实际不会被使用，因为组件使用了 ssr: false）
      return 'temp-id';
    };

    const newTodo: Todo = {
      id: generateId(),
      title,
      status: 'pending',
      createdAt: typeof window !== 'undefined' ? Date.now() : 0,
    };
    setTodos((prevTodos) => [...prevTodos, newTodo]);
  };

  // 切换任务状态
  const toggleTodo = (id: string) => {
    setTodos((prevTodos) =>
      prevTodos.map((todo) =>
        todo.id === id
          ? {
              ...todo,
              status: todo.status === 'pending' ? 'completed' : 'pending',
            }
          : todo
      )
    );
  };

  // 删除任务
  const deleteTodo = (id: string) => {
    setTodos((prevTodos) => prevTodos.filter((todo) => todo.id !== id));
  };

  // 在数据加载完成前显示加载状态，避免水合错误
  if (!isLoaded) {
    return (
      <div className="w-full max-w-2xl mx-auto space-y-8 animate-fade-in">
        <div className="text-center space-y-4 animate-slide-up">
          <h1 className="text-4xl font-bold tracking-tight text-foreground">
            MyTodos
          </h1>
          <p className="text-lg font-medium text-muted-foreground leading-relaxed">
            管理您的待办事项，提高工作效率
          </p>
        </div>
        <div className="text-center py-16">
          <div className="w-8 h-8 mx-auto border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-4 text-sm text-muted-foreground">加载中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto space-y-8 animate-fade-in">
      {/* 标题区域 - 使用字重和颜色建立层级 */}
      <div className="text-center space-y-4 animate-slide-up">
        <h1 className="text-4xl font-bold tracking-tight text-foreground">
          MyTodos
        </h1>
        <p className="text-lg font-medium text-muted-foreground leading-relaxed">
          管理您的待办事项，提高工作效率
        </p>
      </div>

      {/* 添加任务区域 */}
      <div className="space-y-4">
        <AddTodo onAdd={addTodo} />
      </div>

      {/* 任务列表区域 */}
      <div className="space-y-4">
        {todos.length === 0 ? (
          <div className="text-center py-16 animate-fade-in">
            <div className="space-y-4">
              <div className="w-16 h-16 mx-auto bg-muted rounded-full flex items-center justify-center animate-bounce-subtle">
                <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground">还没有任务</h3>
              <p className="text-sm text-muted-foreground max-w-sm mx-auto leading-relaxed">
                创建第一个待办，开始提升效率吧！
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {todos.map((todo) => (
              <TodoItem
                key={todo.id}
                todo={todo}
                onToggle={toggleTodo}
                onDelete={deleteTodo}
              />
            ))}
          </div>
        )}
      </div>

      {/* 统计信息 - 使用更小的字重而非更小字号 */}
      {todos.length > 0 && (
        <div className="text-center">
          <p className="text-base font-medium text-muted-foreground">
            总共 <span className="font-semibold text-foreground">{todos.length}</span> 个任务，
            已完成 <span className="font-semibold text-primary">{todos.filter((todo) => todo.status === 'completed').length}</span> 个
          </p>
        </div>
      )}
    </div>
  );
}
