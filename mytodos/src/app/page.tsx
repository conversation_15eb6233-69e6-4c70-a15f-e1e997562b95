'use client';

import dynamic from 'next/dynamic';

// 动态导入 TodoList 组件，禁用服务端渲染以避免水合错误
const TodoList = dynamic(() => import('@/components/TodoList').then(mod => ({ default: mod.TodoList })), {
  ssr: false,
  loading: () => (
    <div className="w-full max-w-2xl mx-auto space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold tracking-tight text-foreground">
          MyTodos
        </h1>
        <p className="text-lg font-medium text-muted-foreground leading-relaxed">
          管理您的待办事项，提高工作效率
        </p>
      </div>
      <div className="text-center py-16">
        <div className="w-8 h-8 mx-auto border-4 border-primary border-t-transparent rounded-full animate-spin"></div>
        <p className="mt-4 text-sm text-muted-foreground">加载中...</p>
      </div>
    </div>
  )
});

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      {/* 使用 4-Point Grid 系统优化间距 */}
      <div className="container mx-auto px-6 py-12 sm:px-8 sm:py-16">
        <TodoList />
      </div>
    </div>
  );
}
